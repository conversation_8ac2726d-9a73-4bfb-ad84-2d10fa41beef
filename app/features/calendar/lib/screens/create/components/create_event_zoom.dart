import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_calendar/screens/create/create_calendar_event_controller.dart';

import 'create_event_section.dart';

class CreateEventGGMeet extends GetView<CreateCalendarEventController> {
  const CreateEventGGMeet({
    super.key,
  });

  @override
  CreateCalendarEventController get controller =>
      Get.put(CreateCalendarEventController());

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => ExpandableNotifier(
        controller: controller.meetExpanableController,
        child: Expandable(
          collapsed: const Row(),
          expanded: CreateEventSection(
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        LocaleKeys.calendar_create_label_meet.tr,
                        style: textStyle(GPTypography.headingMedium)
                            ?.copyWith(height: 1),
                      ),
                    ),
                    SizedBox(
                      width: 40,
                      height: 26,
                      child: FittedBox(
                        fit: BoxFit.contain,
                        child: CupertinoSwitch(
                          value: controller.hasMeet.value,
                          onChanged: controller.onMeetChanged,
                          activeTrackColor: GPColor.functionAccentWorkSecondary,
                        ),
                      ),
                    ),
                  ],
                ).paddingSymmetric(horizontal: 16, vertical: 12),
                Obx(() => controller.hasMeet.value
                    ? Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(
                                left: 16, right: 16, bottom: 12),
                            child: Row(
                              children: [
                                if (controller.googleEmail.isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.only(right: 8.0),
                                    child: SvgWidget(
                                      'assets/images/svg/ic24-fill-person-checkmark.svg',
                                      width: 16,
                                      height: 16,
                                      color:
                                          GPColor.functionAccentWorkSecondary,
                                    ),
                                  ),
                                Expanded(
                                  child: Text(
                                    controller.googleEmail.isEmpty
                                        ? LocaleKeys
                                            .calendar_create_not_google_signed_in
                                            .tr
                                        : controller.googleEmail,
                                    maxLines: controller.googleEmail.isEmpty
                                        ? null
                                        : 1,
                                    overflow: controller.googleEmail.isEmpty
                                        ? null
                                        : TextOverflow.ellipsis,
                                    style: textStyle(GPTypography.bodyMedium)
                                        ?.copyWith(
                                            color: GPColor.contentSecondary),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // AI Assistant Section
                          _buildAIAssistantSection(),
                        ],
                      )
                    : const SizedBox())
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAIAssistantSection() {
    return Obx(() => Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: [
              // Divider
              Container(
                height: 1,
                color: GPColor.linePrimary,
              ),
              const SizedBox(height: 12),

              // AI Assistant Header with Switch
              Row(
                children: [
                  // Avatar
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: GPColor.contentQuaternary,
                        width: 1,
                      ),
                      gradient: const LinearGradient(
                        colors: [Color(0xFF4BAFF6), Color(0xFFFBA446)],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                    ),
                    child: const Center(
                      child: Text(
                        'AI',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Title and Bot Tag
                  Expanded(
                    child: Row(
                      children: [
                        Text(
                          'AI GapoWork Trợ lý cuộc họp',
                          style: textStyle(GPTypography.headingMedium)
                              ?.copyWith(height: 1),
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(width: 4),
                        // Bot Tag
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 4, vertical: 0),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(
                              color: GPColor.blue,
                              width: 1,
                            ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'Bot',
                            style: textStyle(GPTypography.bodySmall)?.copyWith(
                              color: GPColor.blue,
                              height: 1.33,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  // Switch
                  SizedBox(
                    width: 40,
                    height: 26,
                    child: FittedBox(
                      fit: BoxFit.contain,
                      child: CupertinoSwitch(
                        value: controller.hasAIAssistant.value,
                        onChanged: controller.onAIAssistantChanged,
                        activeTrackColor: GPColor.functionAccentWorkSecondary,
                      ),
                    ),
                  ),
                ],
              ),
              // Description when AI Assistant is enabled
              const SizedBox(height: 8),
              Text(
                'Trợ lý cuộc họp sẽ tham gia Google meet và ghi chú lại nội dung quan trọng',
                style: textStyle(GPTypography.bodyMedium)
                    ?.copyWith(color: GPColor.contentSecondary),
              ),
              const SizedBox(height: 12),
            ],
          ),
        ));
  }
}
